[project]
name = "carta-auth-sidecar"
version = "0.1.0"
description = "ForwardAuth service for CARTA sessions resolving user IDs via Kubernetes labels"
requires-python = ">=3.10"
readme = "README.md"
authors = [{ name = "Science Platform" }]
dependencies = [
  "fastapi>=0.112",
  "uvicorn[standard]>=0.30",
  "cachetools>=5.3",
  "kubernetes>=29.0",
  "structlog>=24.1.0",
  "typer>=0.17.3",
]

[tool.uv]
dev-dependencies = []
package = true

[project.scripts]
carta-sidecar = "carta.cli:main"
carta-intercept = "carta.dev.intercept:app"

[tool.uv.sources]
carta = { path = "src" }

# Ruff configuration - handles formatting, linting, and import sorting
[tool.ruff]
target-version = "py39"
line-length = 88
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "N",    # pep8-naming
    "D",    # pydocstyle
    "UP",   # pyupgrade
    "YTT",  # flake8-2020
    "ANN",  # flake8-annotations
    "ASYNC", # flake8-async
    "S",    # flake8-bandit
    "BLE",  # flake8-blind-except
    "FBT",  # flake8-boolean-trap
    "B",    # flake8-bugbear
    "A",    # flake8-builtins
    "COM",  # flake8-commas
    "C4",   # flake8-comprehensions
    "DTZ",  # flake8-datetimez
    "T10",  # flake8-debugger
    "DJ",   # flake8-django
    "EM",   # flake8-errmsg
    "EXE",  # flake8-executable
    "FA",   # flake8-future-annotations
    "ISC",  # flake8-implicit-str-concat
    "ICN",  # flake8-import-conventions
    "G",    # flake8-logging-format
    "INP",  # flake8-no-pep420
    "PIE",  # flake8-pie
    "T20",  # flake8-print
    "PYI",  # flake8-pyi
    "PT",   # flake8-pytest-style
    "Q",    # flake8-quotes
    "RSE",  # flake8-raise
    "RET",  # flake8-return
    "SLF",  # flake8-self
    "SLOT", # flake8-slots
    "SIM",  # flake8-simplify
    "TID",  # flake8-tidy-imports
    "TCH",  # flake8-type-checking
    "INT",  # flake8-gettext
    "ARG",  # flake8-unused-arguments
    "PTH",  # flake8-use-pathlib
    "ERA",  # eradicate
    "PD",   # pandas-vet
    "PGH",  # pygrep-hooks
    "PL",   # pylint
    "TRY",  # tryceratops
    "FLY",  # flynt
    "NPY",  # numpy
    "AIR",  # airflow
    "PERF", # perflint
    "FURB", # refurb
    "LOG",  # flake8-logging
    "RUF",  # ruff-specific rules
]

ignore = [
    "ANN401",  # Dynamically typed expressions (Any) are disallowed
    "D100",    # Missing docstring in public module
    "D104",    # Missing docstring in public package
    "D107",    # Missing docstring in `__init__`
    "D203",    # 1 blank line required before class docstring
    "D213",    # Multi-line docstring summary should start at the second line
    "FBT001",  # Boolean-typed positional argument in function definition
    "FBT002",  # Boolean default positional argument in function definition
    "PLR0913", # Too many arguments in function definition
    "PLR2004", # Magic value used in comparison
    "S101",    # Use of assert detected (allow in tests)
    "TRY003",  # Avoid specifying long messages outside the exception class
    "COM812",  # missing-trailing-comma
    "PERF203", # try-except inside loop
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401", "F403", "F405"]
"tests/**/*.py" = [
    "S101",    # Use of assert detected
    "ANN",     # Type annotations
    "D103",    # Missing docstring in public function
    "PLR2004", # Magic value used in comparison
    "S105",    # Possible hardcoded password
    "S106",    # Possible hardcoded password
    "S108",    # Probable insecure usage of temporary file/directory
]
"docs/**/*.py" = ["INP001"]

[tool.ruff.lint.isort]
known-first-party = ["canfar"]


[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.pylint]
max-args = 8

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true
docstring-code-line-length = "dynamic"

# MyPy configuration
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_untyped_decorators = true
disallow_untyped_defs = true
ignore_missing_imports = false
implicit_reexport = false
no_implicit_optional = true
extra_checks = true
strict_equality = true
strict_optional = true
plugins = ["pydantic.mypy"]

[[tool.mypy.overrides]]
module = ["tests.*"]
ignore_errors = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "8.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=canfar",
    "--cov-report=term-missing:skip-covered",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
    "-n=auto",  # Enable parallel execution with pytest-xdist
    "--dist=loadfile",  # Use loadfile distribution to ensure tests in same file run in same worker (required for pytest-order)
]
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "unit: marks tests as unit tests",
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "order: marks tests that need to run in a specific order (will run sequentially)",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["canfar"]
omit = [
    "tests/*",
    "setup.py",
    "*/migrations/*",
    "venv/*",
    "*/venv/*",
    ".tox/*",
    "*/site-packages/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if __name__ == .__main__.:",
    "raise NotImplementedError",
    "if TYPE_CHECKING:",
    "pass",
    "@(abc\\.)?abstractmethod",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "raise NotImplementedError",
    "if 0:",
    "if False:",
    "if TYPE_CHECKING:",
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# Bandit security linter configuration
[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_function

# Additional tool configurations for completeness
[tool.vulture]
min_confidence = 80
paths = ["canfar", "tests"]
exclude = ["venv/", ".venv/"]

[tool.interrogate]
ignore-init-method = true
ignore-init-module = false
ignore-magic = false
ignore-semiprivate = false
ignore-private = false
ignore-property-decorators = false
ignore-module = false
ignore-nested-functions = false
ignore-nested-classes = true
ignore-setters = false
fail-under = 80
exclude = ["setup.py", "docs", "build"]
ignore-regex = ["^get$", "^mock_.*", ".*BaseClass.*"]
verbose = 3
quiet = false
whitelist-regex = []
color = true
